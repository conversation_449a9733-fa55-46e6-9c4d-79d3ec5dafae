package engine

import (
	"dxm/siod_sre/auto-scaler/dao"
	"testing"
	"time"
)

// 测试用的简单数据存储
var testNodes = make(map[string]*dao.ClusterNodes)
var testNodesByOffset = make(map[int]*dao.ClusterNodes)

// 简单的测试数据操作函数
func addTestNode(node dao.ClusterNodes) {
	node.LastHeartbeat = time.Now()
	node.Status = dao.NODE_STATUS_ONLINE
	testNodes[node.IP] = &node
	testNodesByOffset[node.Offset] = &node
}

func getTestNodeByIP(ip string) *dao.ClusterNodes {
	return testNodes[ip]
}

func updateTestNodeRole(ip, role string) bool {
	if node := testNodes[ip]; node != nil {
		node.Role = role
		return true
	}
	return false
}

func updateTestNodeHeartbeat(ip string) bool {
	if node := testNodes[ip]; node != nil {
		node.LastHeartbeat = time.Now()
		node.Status = dao.NODE_STATUS_ONLINE
		return true
	}
	return false
}

func getTestMasterNode() *dao.ClusterNodes {
	var masterNode *dao.ClusterNodes
	minOffset := int(^uint(0) >> 1) // 最大int值

	for _, node := range testNodes {
		if node.Status == dao.NODE_STATUS_ONLINE && node.Offset < minOffset {
			masterNode = node
			minOffset = node.Offset
		}
	}
	return masterNode
}

func clearTestData() {
	testNodes = make(map[string]*dao.ClusterNodes)
	testNodesByOffset = make(map[int]*dao.ClusterNodes)
}

func TestMasterSignalControl(t *testing.T) {
	// 初始化引擎
	engine := NewEngine()

	// 测试发送启动信号
	t.Log("Testing start signals...")
	engine.CollectorManager.SendStartSignal()
	engine.CronManager.SendStartSignal()

	// 等待一小段时间让信号处理
	time.Sleep(100 * time.Millisecond)

	// 测试发送停止信号
	t.Log("Testing stop signals...")
	engine.CollectorManager.SendStopSignal()
	engine.CronManager.SendStopSignal()

	// 等待一小段时间让信号处理
	time.Sleep(100 * time.Millisecond)

	t.Log("Signal control test completed")
}

func TestMasterElection(t *testing.T) {
	// 清空测试数据
	clearTestData()

	// 模拟注册多个节点
	nodes := []dao.ClusterNodes{
		{Offset: 1001, Hostname: "node-01", IP: "*************", Role: dao.NODE_ROLE_SLAVE},
		{Offset: 1002, Hostname: "node-02", IP: "*************", Role: dao.NODE_ROLE_SLAVE},
		{Offset: 1003, Hostname: "node-03", IP: "*************", Role: dao.NODE_ROLE_SLAVE},
	}

	// 注册所有节点到测试数据中
	for _, node := range nodes {
		addTestNode(node)
		t.Logf("Registered node: %s (offset: %d)", node.IP, node.Offset)
	}

	// 测试master选举（应该选择offset最小的节点）
	masterNode := getTestMasterNode()
	if masterNode == nil {
		t.Errorf("Failed to get master node")
		return
	}

	// 验证选举结果
	expectedOffset := 1001 // 最小的offset
	if masterNode.Offset != expectedOffset {
		t.Errorf("Expected master offset %d, got %d", expectedOffset, masterNode.Offset)
	}

	expectedIP := "*************"
	if masterNode.IP != expectedIP {
		t.Errorf("Expected master IP %s, got %s", expectedIP, masterNode.IP)
	}

	t.Logf("Master election successful: IP=%s, Offset=%d", masterNode.IP, masterNode.Offset)
}

func TestNodeRoleUpdate(t *testing.T) {
	// 清空测试数据
	clearTestData()

	// 注册一个测试节点
	nodeInfo := dao.ClusterNodes{
		Offset:   2001,
		Hostname: "test-node",
		IP:       "*************",
		Role:     dao.NODE_ROLE_SLAVE,
	}

	addTestNode(nodeInfo)
	t.Logf("Registered test node: %s", nodeInfo.IP)

	// 更新节点角色为master
	if !updateTestNodeRole(nodeInfo.IP, dao.NODE_ROLE_MASTER) {
		t.Errorf("Failed to update node role to master")
		return
	}

	// 验证角色更新
	updatedNode := getTestNodeByIP(nodeInfo.IP)
	if updatedNode == nil {
		t.Errorf("Failed to get updated node")
		return
	}

	if updatedNode.Role != dao.NODE_ROLE_MASTER {
		t.Errorf("Expected role %s, got %s", dao.NODE_ROLE_MASTER, updatedNode.Role)
	}

	// 更新节点角色为slave
	if !updateTestNodeRole(nodeInfo.IP, dao.NODE_ROLE_SLAVE) {
		t.Errorf("Failed to update node role to slave")
		return
	}

	// 验证角色更新
	updatedNode = getTestNodeByIP(nodeInfo.IP)
	if updatedNode == nil {
		t.Errorf("Failed to get updated node")
		return
	}

	if updatedNode.Role != dao.NODE_ROLE_SLAVE {
		t.Errorf("Expected role %s, got %s", dao.NODE_ROLE_SLAVE, updatedNode.Role)
	}

	t.Log("Node role update test passed")
}

func TestHeartbeatUpdate(t *testing.T) {
	// 清空测试数据
	clearTestData()

	// 注册一个测试节点
	nodeInfo := dao.ClusterNodes{
		Offset:   3001,
		Hostname: "heartbeat-test-node",
		IP:       "*************",
		Role:     dao.NODE_ROLE_SLAVE,
	}

	addTestNode(nodeInfo)
	t.Logf("Registered heartbeat test node: %s", nodeInfo.IP)

	// 获取初始心跳时间
	initialNode := getTestNodeByIP(nodeInfo.IP)
	if initialNode == nil {
		t.Errorf("Failed to get initial node")
		return
	}
	initialHeartbeat := initialNode.LastHeartbeat

	// 等待一小段时间
	time.Sleep(100 * time.Millisecond)

	// 更新心跳
	if !updateTestNodeHeartbeat(nodeInfo.IP) {
		t.Errorf("Failed to update heartbeat")
		return
	}

	// 验证心跳时间更新
	updatedNode := getTestNodeByIP(nodeInfo.IP)
	if updatedNode == nil {
		t.Errorf("Failed to get updated node")
		return
	}

	if !updatedNode.LastHeartbeat.After(initialHeartbeat) {
		t.Errorf("Heartbeat time should be updated")
	}

	if updatedNode.Status != dao.NODE_STATUS_ONLINE {
		t.Errorf("Expected status %d, got %d", dao.NODE_STATUS_ONLINE, updatedNode.Status)
	}

	t.Log("Heartbeat update test passed")
}
