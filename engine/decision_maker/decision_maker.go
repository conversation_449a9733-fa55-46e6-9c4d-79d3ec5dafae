package decision_maker

import (
	"dxm/siod-cloud/go-common-lib/ocommon"
	"dxm/siod-cloud/go-common-lib/olog"
	"dxm/siod-cloud/go-common-lib/otool"
	"dxm/siod_sre/auto-scaler/base"
	"dxm/siod_sre/auto-scaler/dao"
	dao_ocean "dxm/siod_sre/auto-scaler/dao/ocean"
	"dxm/siod_sre/auto-scaler/engine/comm"
	"dxm/siod_sre/auto-scaler/engine/plog"
	"dxm/siod_sre/auto-scaler/global"
	"dxm/siod_sre/auto-scaler/pkg/apptree"
	"dxm/siod_sre/auto-scaler/pkg/data_process"
	"encoding/json"
	"fmt"
	"math"
	"time"

	"dxm/siod-cloud/go-common-lib/oquery"
)

type DecisionMaker struct {
	comm.ModuleOneTaskParam
	CanExec        bool
	CantExecReason string
	Action         string
	DecisionReason string
}

var _ IDecisionMaker = &DecisionMaker{}

func (c *DecisionMaker) RunOne() {
	defer func() {
		if c.DecisionInfo.Status != base.ENGINE_TASK_STATUS_SUCCESS && c.DecisionInfo.Status != base.ENGINE_TASK_STATUS_RUNNING {
			c.DecisionInfo.Status = base.ENGINE_TASK_STATUS_FAILED
			c.DecisionInfo.FailedReason = c.CantExecReason
			c.UpdateStatus(base.ENGINE_TASK_STATUS_FAILED)
		}
		c.ModuleOneTaskParam.Notify()
	}()
	olog.Info("start to exec decision maker, %s", c.TraceID)
	if !c.EvaluateStatus() { // 检查当前状态，检查禁止运行、相同任务运行
		return
	}

	if !c.HandleOverloadEmergency() { // 紧急过载处理
		return
	}

	if !c.MakeScalingDecision() { // 检查防抖周期等等
		return
	}

	if !c.AnalyzeCapacityGap() { // 分析容量缺口（重中之重，后续仍需要优化）
		return
	}

	if !c.PredictDemand() { // 预测需求，扩缩容实例数优化
		return
	}

	if !c.ProtectLogic() { // 保护逻辑
		return
	}
	c.StorageDecision()
	if c.TaskInfo.TaskStatus == base.TASK_STATUS_ENABLE {
		comm.ScalerCh.ExecutorChan <- &c.ModuleOneTaskParam
	}
}

func (c *DecisionMaker) Init() (ok bool) {
	c.Phase = comm.DecisionMakerPhase

	// 获取最新的模块状态及任务状态
	var r ocommon.ResultInfo
	var moduleInfo dao.Module
	r = dao.CreateModulePtr().SearchByPk(&moduleInfo, c.ModuleInfo.ID)
	if !r.IsOk() {
		plog.EngineLG.WithFields(c.Fields()).Errorf("get module info failed when decision init, err:%v", r)
		return
	}

	var taskInfo dao.TaskInfo
	r = dao.CreateTaskInfoPtr().SearchByPk(&taskInfo, c.TaskInfo.ID)
	if !r.IsOk() {
		plog.EngineLG.WithFields(c.Fields()).Errorf("get task info failed when decision init, err:%v", r)
		return
	}

	var ruleInfo dao.RuleOnline
	r = dao.CreateRuleOnlinePtr().SearchByPk(&ruleInfo, c.RuleInfo.ID)
	if !r.IsOk() {
		plog.EngineLG.WithFields(c.Fields()).Errorf("get rule info failed when decision init, err:%v", r)
		return
	}

	c.ModuleInfo = moduleInfo
	c.TaskInfo = taskInfo
	c.RuleInfo = ruleInfo
	return true
}

func (c *DecisionMaker) GenName() string {
	// 任务类型_模块名_模块ID_任务ID_规则ID
	return fmt.Sprintf("%s_%d_%d_%d", c.TaskInfo.TaskType, c.ModuleInfo.ID, c.TaskInfo.ID, c.RuleInfo.ID)
}

// 根据当前状态 和 历史数据判断是否可执行
func (c *DecisionMaker) EvaluateStatus() (ok bool) {

	if !c.canRunByDisableScaleDown() {
		return
	}

	// 获取当前的实例数量
	if !c.getModuleInstanceInfo() {
		return
	}

	if !c.canRunByDisableTask() { // 禁止自动化任务运行
		return
	}

	if !c.canRunBySameTask() { // 禁止同一服务同一机房同时运行
		return
	}

	if !c.canRunByCronTask() { // 禁止定时任务运行逻辑
		return
	}
	return true
}

func (c *DecisionMaker) MakeScalingDecision() (ok bool) {
	// 通过防抖周期是否需要扩缩容
	if !c.canRunByShakeCycle() {
		return
	}

	return true
}

// 分析容量缺口，计算扩容和缩容数量（目标值）
func (c *DecisionMaker) AnalyzeCapacityGap() (ok bool) {
	// 手动任务直接运行，不检查
	// 定时任务根据配置信息进行计算
	// 自动化任务根据配置指标进行计算
	switch c.TaskInfo.TaskType {
	case base.TASK_TYPE_MANUAL:
		if !c.getCapacityGapForMunual() {
			return
		}
	case base.TASK_TYPE_CRONTAB:
		if !c.getCapacityGapForCron() {
			return
		}
	case base.TASK_TYPE_AUTO_TASK:
		if !c.getCapacityGapForAutoTask() {
			return
		}
	}

	return true
}

// 评估当前容量
func (c *DecisionMaker) EvaluateCurrentCapacity() (ok bool) {
	return true
}

// 紧急过载处理函数
func (c *DecisionMaker) HandleOverloadEmergency() (ok bool) {
	return true
}

// 预测需求，预测接下来一段时间各需要多少实例，趋势和计划
func (c *DecisionMaker) PredictDemand() (ok bool) {
	return true
}

// 资源优化函数
func (c *DecisionMaker) OptimizeAllocation() (ok bool) {
	return true
}

// 策略调整函数
func (c *DecisionMaker) AdjustDecisionPolicy() (ok bool) {
	return true
}

// 回滚计划
func (c *DecisionMaker) GenerateRollbackPlan() (ok bool) {
	return true
}

// 自动任务禁止运行逻辑校验,true：可运行，false：禁止运行
func (c *DecisionMaker) canRunByDisableTask() (ok bool) {
	// 禁止执行配置
	switch c.TaskInfo.TaskType { // 自动化任务是否可以执行
	case base.TASK_TYPE_AUTO_TASK:
		if c.ModuleInfo.DisableAutoTask { // 禁止自动任务开启
			if c.ModuleInfo.DisableTime.Before(time.Now()) && c.ModuleInfo.DisableTime != global.ZeroTime {
				// 若禁止自动任务截止时间早于当前时间，则可以执行
				// 禁止自动任务截止时间，若为初始时间则永久禁止
				c.CanExec = true
				// 更新为开启自动任务模式
				dao.CreateModulePtr().UpdateByPk(
					&dao.Module{DisableAutoTask: false, DisableTime: global.ZeroTime},
					[]string{"DisableAutoTask", "DisableTime"},
					c.ModuleInfo.ID,
				)
			} else { // 其他情况禁止执行
				c.CanExec = false
				c.CantExecReason = CANT_EXEC_MODULE_DISABLE
				plog.EngineLG.WithFields(c.Fields()).Warn("module disable auto task")
				return
			}
		} else { // 禁止自动任务关闭，则可以进行
			c.CanExec = true
		}
	default: // 定时任务和手动任务受该配置影响
		c.CanExec = true
	}

	plog.EngineLG.WithFields(c.Fields()).Infof("auto task can run by disable, can_run:%v", c.CanExec)
	return c.CanExec
}

/*
同一模块同一机房任务不可同时运行（这里任务优先级的问题待处理）
目前手动任务优先级最高，存在其他任务也可以执行手动任务，但其他任务暂不处理
*/
func (c *DecisionMaker) canRunBySameTask() bool {
	c.CanExec = false
	var r ocommon.ResultInfo
	var decisionList []dao.DecisionRecord
	query := oquery.NewQueryStructOfTable()
	query.AddConditonsByOperator("ModuleId", oquery.OP_EQUAL, c.ModuleInfo.ID)
	query.AddConditonsByOperator("TaskId", oquery.OP_EQUAL, c.TaskInfo.ID)
	query.AddConditonsByOperator("RuleId", oquery.OP_EQUAL, c.RuleInfo.ID)
	query.AddConditonsByOperator("IdcTag", oquery.OP_EQUAL, c.TaskInfo.IdcTag)
	query.AddConditonsByOperator("Status", oquery.OP_EQUAL, base.ENGINE_TASK_STATUS_RUNNING)
	query.AddConditonsByOperator("TaskStatus", oquery.OP_NOT_EQUAL, base.TASK_STATUS_TEST_RUN)
	r = dao.CreateDecisionRecordPtr().SearchByQuery(&decisionList, query)
	if !r.IsOk() {
		c.CantExecReason = CANT_EXEC_DB_ERROR
		plog.EngineLG.WithFields(c.Fields()).Errorf("get same task from database failed, err:%v", r)
		return false
	}

	var manualNum, cronNum, autoNum, total int
	for _, v := range decisionList {
		switch v.TaskType {
		case base.TASK_TYPE_MANUAL:
			manualNum++
			total++
		case base.TASK_TYPE_CRONTAB:
			cronNum++
			total++
		case base.TASK_TYPE_AUTO_TASK:
			autoNum++
			total++
		}
	}
	plog.EngineLG.WithFields(c.Fields()).Debugf("same task check, manual_num:%d, cron_num:%d, auto_num:%d", manualNum, cronNum, autoNum)
	switch c.TaskInfo.TaskType {
	case base.TASK_TYPE_MANUAL: // 手动任务，优先级最高，执行该任务并撤销其他任务
		if manualNum > 0 {
			// todo：后续存在手动任务，则需要排队处理，在提交前确认
			c.CanExec = false
		} else {
			c.CanExec = true
		}
	case base.TASK_TYPE_CRONTAB: // 定时任务，优先级次之，暂时和自动化任务同优先级，后续优化
		if manualNum > 0 || cronNum > 0 {
			c.CanExec = false
		} else {
			c.CanExec = true
		}
	case base.TASK_TYPE_AUTO_TASK: // 自动化任务，优先级最低
		if total > 0 {
			c.CanExec = false
		} else {
			c.CanExec = true
		}
	}

	if !c.CanExec {
		c.CantExecReason = CANT_EXEC_SAME_TASK
	}

	plog.EngineLG.WithFields(c.Fields()).Debugf("same task check, task_type:%s, can_run:%v", c.TaskInfo.TaskType, c.CanExec)
	return c.CanExec
}

func (c *DecisionMaker) canRunByDisableScaleDown() bool {
	if c.TaskInfo.TaskType != base.TASK_TYPE_AUTO_TASK {
		return true
	}
	c.CanExec = false
	if c.EventInfo.Action != base.ACTION_DOWN {
		c.CanExec = true
	} else {
		if c.RuleInfo.TargetCanScaling {
			c.CanExec = true
		} else {
			c.CanExec = false
		}
	}

	if !c.CanExec {
		c.CantExecReason = CANT_EXEC_AUTO_TASK_DISABLE
	}

	plog.EngineLG.WithFields(c.Fields()).Debugf("disable scale down check, task_type:%s, can_run:%v", c.TaskInfo.TaskType, c.CanExec)
	return c.CanExec
}

func (c *DecisionMaker) canRunByShakeCycle() (ok bool) {
	if c.TaskInfo.TaskType != base.TASK_TYPE_AUTO_TASK { // 非自动化任务，无需抖动周期检验
		return true
	}

	// 自动化任务，抖动周期逻辑，查询相关事件
	var eventList []dao.EventInfo
	var r ocommon.ResultInfo
	daoEventPtr := dao.CreateEventInfoPtr()
	query := oquery.NewQueryStructOfTable()
	query.AddConditonsByOperator("ModuleId", oquery.OP_EQUAL, c.ModuleInfo.ID)
	query.AddConditonsByOperator("TaskId", oquery.OP_EQUAL, c.TaskInfo.ID)
	query.AddConditonsByOperator("RuleId", oquery.OP_EQUAL, c.RuleInfo.ID)
	query.AddConditonsByOperator("Action", oquery.OP_EQUAL, c.EventInfo.Action)
	query.AddConditonsByOperator("CreateTime", oquery.OP_GRATER, time.Now().Add(-time.Duration(c.RuleInfo.TargetPeriod)*time.Minute))
	r = daoEventPtr.SearchByQuery(&eventList, query)
	if !r.IsOk() {
		plog.EngineLG.WithFields(c.Fields()).Errorf("get event info from database when check shake cycle failed, err:%v", r)
		return false
	}

	if len(eventList) >= (c.RuleInfo.TargetTriggerTimes - 1) {
		c.CanExec = true
	} else {
		c.CanExec = false
		c.CantExecReason = CANT_EXEC_SHAKE_CYCLE_INVAILD
	}

	plog.EngineLG.WithFields(c.Fields()).Debugf("target_period:[%d], target_trigger_times:[%d], can_run:%v",
		c.RuleInfo.TargetPeriod, c.RuleInfo.TargetTriggerTimes, c.CanExec)
	return c.CanExec
}

// 获取执行时实例的信息
func (c *DecisionMaker) getModuleInstanceInfo() (ok bool) {

	var healthNum, disableIns, abnormalNum, totalNum int
	switch c.ModuleInfo.ModuleType {
	case base.MODULE_TYPE_MODEL:
		var insCountList []int
		var startTime = time.Now().Add(-20 * time.Minute).Format(otool.TIME_FORMAT_STR)
		var endTime = time.Now().Format(otool.TIME_FORMAT_STR)
		dataList := dao_ocean.OceanDetailSearchLatestBetweenTime("risk_model", c.ModuleInfo.ServiceName, startTime, endTime)
		for i, v := range dataList {
			insCountList = append(insCountList, v.InsCount)
			if i > 10 {
				break
			}
		}

		sumIns, _ := data_process.Sum(insCountList)
		healthNum = int(math.Round(float64(sumIns.(int)) / float64(len(insCountList))))
		totalNum = healthNum
	case base.MODULE_TYPE_POD:
		instanceList, err := apptree.GetInstanceByBNSWithDisabledV2(c.ModuleInfo.ServiceName)
		if err != nil {
			c.CantExecReason = CAT_EXEC_GET_CURRENT_INSTANCE_FAILED
			plog.EngineLG.WithFields(c.Fields()).Errorf("get instance info failed when get module instance info, err:%v", err)
			return
		}

		for _, ins := range instanceList {
			if v, ok := ins.Tags["idc"]; ok {
				if v == c.EventInfo.LogicIdc {
					if ins.Disable == 1 {
						disableIns++
						totalNum++
					} else if ins.Status != 0 {
						abnormalNum++
						totalNum++
					} else {
						healthNum++
						totalNum++
					}
				}
			}
		}
	}

	c.InstanceInfo = dao.InstanceInfo{
		DisableNum:  disableIns,
		HealthNum:   healthNum,
		AbnormalNum: abnormalNum,
		TotalNum:    totalNum,
	}
	plog.EngineLG.WithFields(c.Fields()).Debugf("get instance info, disable_num:%d, health_num:%d, abnormal_num:%d, total_num:%d",
		disableIns, healthNum, abnormalNum, totalNum)
	return true
}

// 定时任务是否可以执行，验证定时任务执行逻辑
func (c *DecisionMaker) canRunByCronTask() (ok bool) {
	if c.TaskInfo.TaskType != base.TASK_TYPE_CRONTAB {
		return true
	}
	c.CanExec = false

	// 检查任务执行前的任务是否已执行
	// 1. 检查上个任务是否执行相应的动作
	// 2. 补充检查当前实例数是否负责相应的执行动作
	var r ocommon.ResultInfo
	query := oquery.NewQueryStructOfTable()
	query.AddConditonsByOperator("ModuleId", oquery.OP_EQUAL, c.ModuleInfo.ID)
	query.AddConditonsByOperator("TaskId", oquery.OP_EQUAL, c.TaskInfo.ID)
	query.AddOrders(oquery.Order{Name: "ID", IsASC: false})
	var decisionList []dao.DecisionRecord
	r = dao.CreateDecisionRecordPtr().SearchByQuery(&decisionList, query)
	if !r.IsOk() {
		c.CantExecReason = CANT_EXEC_DB_ERROR
		plog.EngineLG.WithFields(c.Fields()).Errorf("get decision record failed from database when check run by cron task, err:%v", r)
		return
	}

	// 判断扩容和缩容
	if c.TaskInfo.SchedScaleInRule == c.RuleInfo.ID { // 缩容，校验当前总实例数是否符合
		if data_process.Within10Percent(c.RuleInfo.CurrentNum, c.InstanceInfo.TotalNum) { // 如果相差不在10%以内，则可以执行
			c.CanExec = true
		} else {
			c.CanExec = false
			c.CantExecReason = CANT_EXEC_CRON_NOT_LAST_ACTION
		}
		plog.EngineLG.WithFields(c.Fields()).Debugf("cron task diff when scale in, curr:%d, total:%d, can_run:%v",
			c.RuleInfo.CurrentNum, c.InstanceInfo.TotalNum, c.CanExec)
	} else { // 扩容，校验前一个任务是否为缩容，同时校验当前实例数是否满足扩容
		if len(decisionList) == 0 { // 如果执行的任务数量为0,则无法执行
			c.CantExecReason = CANT_EXEC_CRON_NOT_LAST_ACTION
			plog.EngineLG.WithFields(c.Fields()).Debugf("cron task can run when scale out, first task")
			return false
		}

		if decisionList[0].RuleId == c.TaskInfo.SchedScaleInRule { // 上一次动作为缩容
			if data_process.Within10Percent(c.RuleInfo.CurrentNum-c.RuleInfo.AdjustNum, c.InstanceInfo.TotalNum) { // 如果相差不在10%以内，则可以执行
				c.CanExec = true
			} else {
				c.CanExec = false
				c.CantExecReason = CANT_EXEC_CRON_NOT_LAST_ACTION
			}
		}
		plog.EngineLG.WithFields(c.Fields()).Debugf("cron task diff when scale out, curr:%d, total:%d, can_run:%v",
			c.RuleInfo.CurrentNum, c.InstanceInfo.TotalNum, c.CanExec)
	}

	return c.CanExec
}

func (c *DecisionMaker) getCapacityGapForMunual() (ok bool) {
	c.CanExec = false

	var expectNum int
	// 预期扩容动作，但实际为实例缩容(通过可用实例获取)
	switch c.RuleInfo.ManualAction {
	case base.ACTION_UP:
		c.Action = base.ACTION_UP
		expectNum = c.RuleInfo.AvailNum + c.RuleInfo.AdjustNum
		if expectNum <= c.InstanceInfo.HealthNum { // 扩容预期实例数比目前还少，则判定执行存在问题
			c.CantExecReason = CANT_EXEC_EXPANSION_LESS_THAN_NOW
			return
		}

		c.ProcessInfo = dao.ProcessInfo{
			DecisionNum:       c.RuleInfo.CurrentNum + c.RuleInfo.AdjustNum,
			DecisionHealthNum: c.RuleInfo.AvailNum + c.RuleInfo.AdjustNum,
			FinalNum:          c.RuleInfo.CurrentNum + c.RuleInfo.AdjustNum,
		}

	case base.ACTION_DOWN:
		c.Action = base.ACTION_DOWN
		expectNum = c.RuleInfo.AvailNum - c.RuleInfo.AdjustNum

		if c.TaskInfo.TaskType != base.TASK_TYPE_MANUAL {
			if expectNum >= c.InstanceInfo.HealthNum { // 执行缩容命令，但是期望实例数比当前实例数还多，判定存在问题
				c.CantExecReason = CANT_EXEC_SHRINK_MORE_THAN_NOW
				plog.EngineLG.WithFields(c.Fields()).Debugf("exec shrink more than now, expect:%d, health_num:%d", expectNum, c.InstanceInfo.HealthNum)
				return
			}
		}

		c.ProcessInfo = dao.ProcessInfo{
			DecisionNum:       c.RuleInfo.CurrentNum - c.RuleInfo.AdjustNum,
			DecisionHealthNum: c.RuleInfo.AvailNum - c.RuleInfo.AdjustNum,
			FinalNum:          c.RuleInfo.CurrentNum - c.RuleInfo.AdjustNum,
		}
	default:
		c.CanExec = false
		c.CantExecReason = "action invaild when get capacity gap for munual"
		plog.EngineLG.WithFields(c.Fields()).Errorf("action invaild when get capacity gap for munual, action:%s", c.RuleInfo.ManualAction)
		return
	}

	plog.EngineLG.WithFields(c.Fields()).Debugf("get capacity gap for munual, decision_num:%d, health_num:%d, final_num:%d",
		c.ProcessInfo.DecisionNum, c.ProcessInfo.DecisionHealthNum, c.ProcessInfo.FinalNum)
	c.CanExec = true
	return true
}

// 为定时任务获取实例数量
func (c *DecisionMaker) getCapacityGapForCron() bool {
	c.CanExec = false

	// 判断扩容场景还是缩容场景
	var expectTotalNum, expectHealthNum, actualTotalNum, actualHealthNum int
	if c.TaskInfo.SchedScaleInRule == c.RuleInfo.ID { // 缩容
		c.Action = base.ACTION_DOWN
		expectTotalNum = c.RuleInfo.CurrentNum - c.RuleInfo.AdjustNum
		expectHealthNum = c.RuleInfo.AvailNum - c.RuleInfo.AdjustNum
		actualTotalNum = c.InstanceInfo.TotalNum - c.RuleInfo.AdjustNum
		actualHealthNum = c.InstanceInfo.HealthNum - c.RuleInfo.AdjustNum

		if expectHealthNum != actualHealthNum || expectTotalNum != actualTotalNum {
			// todo: 通告提醒并确认修正
		}

		c.ProcessInfo = dao.ProcessInfo{
			DecisionNum:       c.RuleInfo.CurrentNum - c.RuleInfo.AdjustNum,
			DecisionHealthNum: c.InstanceInfo.HealthNum - c.RuleInfo.AdjustNum,
			FinalNum:          c.RuleInfo.CurrentNum - c.RuleInfo.AdjustNum,
		}
	} else { //扩容
		c.Action = base.ACTION_UP
		expectTotalNum = c.RuleInfo.CurrentNum
		expectHealthNum = c.RuleInfo.AvailNum
		actualTotalNum = c.InstanceInfo.TotalNum + c.RuleInfo.AdjustNum
		actualHealthNum = c.InstanceInfo.HealthNum + c.RuleInfo.AdjustNum

		if expectHealthNum != actualHealthNum || expectTotalNum != actualTotalNum {
			// todo: 通告提醒并确认修正
		}

		c.ProcessInfo = dao.ProcessInfo{
			DecisionNum:       c.RuleInfo.CurrentNum,
			DecisionHealthNum: c.InstanceInfo.HealthNum + c.RuleInfo.AdjustNum,
			FinalNum:          c.RuleInfo.CurrentNum,
		}
	}

	plog.EngineLG.WithFields(c.Fields()).Debugf("get capacity gap for cron, action:%s, decision_num:%d, health_num:%d, final_num:%d",
		c.Action, c.ProcessInfo.DecisionNum, c.ProcessInfo.DecisionHealthNum, c.ProcessInfo.FinalNum)

	c.CanExec = true
	return true
}

// 为自动化任务获取实例
func (c *DecisionMaker) getCapacityGapForAutoTask() (ok bool) {
	var metricList []base.MetricInfo
	err := json.Unmarshal([]byte(c.RuleInfo.TargetMetric), &metricList)
	if err != nil {
		plog.EngineLG.WithFields(c.Fields()).Errorf("json unmarshal failed when get capacity gap for auto task, %s", err)
		return
	}

	var metricMap = make(map[string]base.MetricInfo)
	for _, metric := range metricList { //最近10个周期的点作为平均值，同期
		metricMap[metric.MetricName] = metric
	}
	expectNum := c.getMetricValueByHistory(metricMap)

	c.ProcessInfo = dao.ProcessInfo{
		DecisionNum:       int(expectNum) + c.InstanceInfo.DisableNum + c.InstanceInfo.AbnormalNum,
		DecisionHealthNum: int(expectNum),
		FinalNum:          int(expectNum) + c.InstanceInfo.DisableNum + c.InstanceInfo.AbnormalNum,
	}
	if c.ProcessInfo.FinalNum == c.InstanceInfo.TotalNum {
		c.CantExecReason = "no need to execute"
		plog.EngineLG.WithFields(c.Fields()).Debugf("no need to execute when get capacity gap for auto task, action:%s, decision_num:%d, health_num:%d, final_num:%d",
			c.Action, c.ProcessInfo.DecisionNum, c.ProcessInfo.DecisionHealthNum, c.ProcessInfo.FinalNum)
		return
	}
	c.Action = c.EventInfo.Action
	plog.EngineLG.WithFields(c.Fields()).Debugf("get capacity gap for auto task, action:%s, decision_num:%d, health_num:%d, final_num:%d",
		c.Action, c.ProcessInfo.DecisionNum, c.ProcessInfo.DecisionHealthNum, c.ProcessInfo.FinalNum)
	return true
}

// 根据历史同期同比，获取当前指标值并拟合
func (c *DecisionMaker) getMetricValueByHistory(metricMap map[string]base.MetricInfo) float64 {
	var currMonitor, oneWeekAgoMonitor, oneWeekHourMonitor dao.MonitorInfo
	currMonitor = c.getOneMetricByTime(time.Now())
	oneWeekAgoMonitor = c.getOneMetricByTime(time.Now().Add(-7 * 24 * time.Hour))
	oneWeekHourMonitor = c.getOneMetricByTime(time.Now().Add(-7*24*time.Hour + 30*time.Minute)) // 一周前同时段30分钟后
	/* 判断容量水位优先级
	1. cpu使用率：表示服务总体计算资源使用，可用来计算实例数量
	2. 服务cpu使用率：表示服务应用计算资源使用，可用来计算实例数量
	3. 单实例QPS：表示单个实例总体承载流量情况，可用来计算实例数量
	4. 服务耗时：表示当前服务总体耗时，用来辅助计算实例数量
	5. 容量水位：表示服务总体容量水位情况，用来辅助计算实例数量
	6. http错误码：表示当前错误数量情况，用来辅助计算实例数量
	*/

	type needIns struct {
		metricName string
		needInsNum float64
	}
	var needInsList []needIns
	if metric, exist := metricMap[base.METRIC_NAME_CPU_PERCENT]; exist {
		var needT needIns
		needT.metricName = metric.MetricName
		needT.needInsNum = c.computeTargeInstance(base.METRIC_NAME_CPU_PERCENT, metric.TargetValue,
			currMonitor.CPUAvg, oneWeekAgoMonitor.CPUAvg, oneWeekHourMonitor.CPUAvg)
		needInsList = append(needInsList, needT)
		plog.EngineLG.WithFields(c.Fields()).Debugf("get cpu metric value by history, %v", needT)
	}

	if metric, exist := metricMap[base.METRIC_NAME_APP_CPU_PERCENT]; exist {
		var needT needIns
		needT.metricName = metric.MetricName
		needT.needInsNum = c.computeTargeInstance(base.METRIC_NAME_APP_CPU_PERCENT, metric.TargetValue,
			currMonitor.CPUServ, oneWeekAgoMonitor.CPUServ, oneWeekHourMonitor.CPUServ)
		needInsList = append(needInsList, needT)
		plog.EngineLG.WithFields(c.Fields()).Debugf("get cpu serv metric value by history, %v", needT)
	}

	if metric, exist := metricMap[base.METRIC_NAME_SINGLE_INSTANCE_QPS]; exist {
		var needT needIns
		needT.metricName = metric.MetricName
		needT.needInsNum = c.computeTargeInstance(base.METRIC_NAME_SINGLE_INSTANCE_QPS, metric.TargetValue,
			currMonitor.QPSAvg, oneWeekAgoMonitor.QPSAvg, oneWeekHourMonitor.QPSAvg)
		needInsList = append(needInsList, needT)
		plog.EngineLG.WithFields(c.Fields()).Debugf("get qps metric value by history, %v", needT)
	}

	if metric, exist := metricMap[base.METRIC_NAME_AVERAGE_COST]; exist {
		// 首先判断是否是容量造成COST上涨，qps、cpu是否同比上涨
		growthPercentCpu := (oneWeekAgoMonitor.CPUAvg - currMonitor.CPUAvg) / currMonitor.CPUAvg
		growthPercentQps := (oneWeekAgoMonitor.QPSAvg - currMonitor.QPSAvg) / currMonitor.QPSAvg

		if growthPercentCpu > 0.2 || growthPercentQps > 0.2 {
			c.DecisionReason += fmt.Sprintf("\n%s：根据信息可得出容量造成cost上涨。", metric.MetricName)
			plog.EngineLG.WithFields(c.Fields()).Debugf("get cost metric value by history, %s", metric.MetricName)
		}
	}

	// if metric, exist := metricMap[base.METRIC_NAME_WATER_LEVEL]; exist {

	// }

	if metric, exist := metricMap[base.METRIC_NAME_HTTP_CODE_NUM]; exist && c.ModuleInfo.ModuleType == base.MODULE_TYPE_MODEL {
		// 首先判断是否是容量造成httpcode上涨，qps、cpu是否同比上涨
		growthPercentCpu := (oneWeekAgoMonitor.CPUAvg - currMonitor.CPUAvg) / currMonitor.CPUAvg
		growthPercentQps := (oneWeekAgoMonitor.QPSAvg - currMonitor.QPSAvg) / currMonitor.QPSAvg

		if growthPercentCpu > 0.2 || growthPercentQps > 0.2 {
			c.DecisionReason += fmt.Sprintf("\n%s：根据信息可得出容量造成httpcode上涨。", metric.MetricName)
			plog.EngineLG.WithFields(c.Fields()).Debugf("get http code metric value by history, %s", metric.MetricName)
		}
	}

	var resultNum float64
	for _, needIns := range needInsList {
		if resultNum < needIns.needInsNum {
			resultNum = needIns.needInsNum
		}
	}
	plog.EngineLG.WithFields(c.Fields()).Debugf("get metric need value by history, need_ins:%.2f", resultNum)
	return resultNum
}

func (c *DecisionMaker) computeTargeInstance(metricName string, targetValue, currValue, oneWeekAgoValue, oneWeekHourValue float64) float64 {
	c.DecisionReason += fmt.Sprintf("\n%s：", metricName)
	var needInsNum, growthInsNum float64
	// 根据当前值和目标值计算计算所需实例数
	needInsNum = float64(c.InstanceInfo.HealthNum) * currValue / (targetValue + 0.01)
	c.DecisionReason += fmt.Sprintf("健康实例数为%d，当前值为%.2f， 目标值为%.2f，计算目标实例数为%.1f。",
		c.InstanceInfo.HealthNum, currValue, targetValue, needInsNum)

	// 计算一周前同时间段和半小时后的数值偏差，判断容量趋势
	growthPercent := (oneWeekHourValue - oneWeekAgoValue) / (oneWeekAgoValue + 0.01)
	if growthPercent > 0.10 { // 流量增长趋势，目标值期望值降低
		growthInsNum = float64(c.InstanceInfo.HealthNum) * currValue / (targetValue - 0.1)
		c.DecisionReason += fmt.Sprintf("判断后续为增长趋势，因而优化后目前实例数为%.1f.", growthInsNum)
	}
	plog.EngineLG.WithFields(c.Fields()).Debugf("compute targe instance, growth_ins_num:%.2f, need_ins_num:%.2f", growthInsNum, needInsNum)
	if growthInsNum > needInsNum {
		needInsNum = growthInsNum
	}

	return needInsNum
}

// 获取同一段时间内10分钟的采集指标
func (c *DecisionMaker) getOneMetricByTime(timeT time.Time) (daoInfo dao.MonitorInfo) {
	var daoMonitorPtr = dao.CreateMonitorInfoPtr()
	var querySlice []dao.MonitorInfo
	query := oquery.NewQueryStructOfTable()
	query.AddConditonsByOperator("ModuleID", oquery.OP_EQUAL, c.ModuleInfo.ID)
	query.AddConditonsByOperator("ServiceName", oquery.OP_EQUAL, c.ModuleInfo.ServiceName)
	query.AddConditonsByOperator("LogicIDC", oquery.OP_EQUAL, c.TaskInfo.IdcTag)
	query.AddConditonsByOperator("CollectTime", oquery.OP_GRATER, timeT.Add(-10*time.Minute).Format(otool.TIME_FORMAT_STR))
	query.AddConditonsByOperator("CollectTime", oquery.OP_LESS, timeT.Format(otool.TIME_FORMAT_STR))

	r := daoMonitorPtr.SearchByQuery(&querySlice, query)
	if !r.IsOk() {
		plog.EngineLG.WithFields(c.Fields()).Debugf("get monitor info failed from database when get metric value by history, err:%v", r)
		return
	}

	var sliceNum float64 = float64(len(querySlice))
	for _, row := range querySlice {
		daoInfo.CPUAvg += row.CPUAvg
		daoInfo.CPUServ += row.CPUServ
		daoInfo.QPSAvg += row.QPSAvg
		daoInfo.CostAvg += row.CostAvg
		daoInfo.MemAvg += row.MemAvg
		daoInfo.Level += row.Level
	}

	daoInfo.CPUAvg = daoInfo.CPUAvg / (sliceNum + 0.01)
	daoInfo.CPUServ = daoInfo.CPUServ / (sliceNum + 0.01)
	daoInfo.QPSAvg = daoInfo.QPSAvg / (sliceNum + 0.01)
	daoInfo.CostAvg = daoInfo.CostAvg / (sliceNum + 0.01)
	daoInfo.MemAvg = daoInfo.MemAvg / (sliceNum + 0.01)
	daoInfo.Level = daoInfo.Level / (sliceNum + 0.01)

	plog.EngineLG.WithFields(c.Fields()).Debugf("get all metric value by history, num:%.2f, cpu:%.2f, qps:%.2f, cpu:%.2f, cost:%.2f, mem:%.2f, level:%.2f",
		sliceNum, daoInfo.CPUAvg, daoInfo.CPUServ, daoInfo.QPSAvg, daoInfo.CostAvg, daoInfo.MemAvg, daoInfo.Level)
	return
}
func (c *DecisionMaker) StorageDecision() {
	var (
		failedReason    string
		adjustmentValue int
		r               ocommon.ResultInfo
		status          int
		decisionId      int
	)

	var daoDecisionPtr = dao.CreateDecisionRecordPtr()
	instanceInfo, _ := json.Marshal(&c.InstanceInfo)
	processInfo, _ := json.Marshal(&c.ProcessInfo)

	if c.CantExecReason != "" {
		failedReason = c.CantExecReason
	}

	if c.Action == base.ACTION_UP {
		adjustmentValue = c.ProcessInfo.FinalNum - c.InstanceInfo.TotalNum
	} else {
		adjustmentValue = c.InstanceInfo.TotalNum - c.ProcessInfo.FinalNum
	}
	status = base.ENGINE_TASK_STATUS_RUNNING
	if c.TaskInfo.TaskStatus == base.TASK_STATUS_TEST_RUN {
		status = base.ENGINE_TASK_STATUS_SUCCESS
	}

	d := dao.DecisionRecord{
		ModuleId:        c.ModuleInfo.ID,
		ServiceName:     c.ModuleInfo.ServiceName,
		TaskId:          c.TaskInfo.ID,
		RuleId:          c.RuleInfo.ID,
		IdcTag:          c.TaskInfo.IdcTag,
		TaskType:        c.TaskInfo.TaskType,
		Status:          status,
		TaskStatus:      c.TaskInfo.TaskStatus,
		CurrentNum:      c.InstanceInfo.TotalNum,
		Action:          c.Action,
		AdjustmentValue: adjustmentValue,
		HitMetric:       c.EventInfo.RuleInfo,
		FailedReason:    failedReason,
		ProcessInfo:     string(processInfo),
		InstanceInfo:    string(instanceInfo),
		LastModifyTime:  time.Now(),
	}
	decisionId, r = daoDecisionPtr.Insert(&d)
	if !r.IsOk() {
		plog.EngineLG.WithFields(c.Fields()).Errorf("insert decision to database failed, err:[%v]", r)
	}
	d.ID = decisionId
	c.DecisionInfo = d
}

func (c *DecisionMaker) UpdateStatus(status int) {
	dao.CreateDecisionRecordPtr().UpdateByPk(&dao.DecisionRecord{Status: status}, []string{"Status"}, c.DecisionInfo.ID)
	if c.TaskInfo.TaskType == base.TASK_TYPE_CRONTAB {
		dao.CreateTaskInfoPtr().UpdateByPk(&dao.TaskInfo{TaskStatus: base.TASK_MANUAL_STATUS_FAILED}, []string{"TaskStatus"}, c.TaskInfo.ID)
	}
}

func (c *DecisionMaker) Fields() map[string]interface{} {
	return map[string]interface{}{
		"module_id":   c.ModuleInfo.ID,
		"module_name": c.ModuleInfo.Name,
		"task_id":     c.TaskInfo.ID,
		"rule_id":     c.RuleInfo.ID,
		"trace_id":    c.TraceID,
	}
}
