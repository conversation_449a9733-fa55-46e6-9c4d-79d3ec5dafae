# Auto-Scaler 主从切换设计文档

## 1. 概述

Auto-Scaler 系统采用主从架构设计，通过心跳检测和选举机制实现高可用性。系统中只有主节点（Master）执行实际的扩缩容任务，从节点（Slave）处于待机状态，当主节点故障时自动进行故障转移。

## 2. 核心设计原则

### 2.1 选举策略
- **基于 Offset 的选举**：系统采用最小 Offset 优先的选举策略
- **心跳检测**：通过定期心跳更新确保节点活跃性
- **故障检测**：心跳超时机制检测节点故障

### 2.2 高可用保证
- **自动故障转移**：主节点故障时自动选举新的主节点
- **服务连续性**：确保扩缩容服务不中断
- **数据一致性**：通过数据库状态同步保证一致性

## 3. 系统架构

### 3.1 节点管理
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Master Node   │    │   Slave Node    │    │   Slave Node    │
│   (Offset: 1)   │    │   (Offset: 2)   │    │   (Offset: 3)   │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ ✓ Collector     │    │ ✗ Collector     │    │ ✗ Collector     │
│ ✓ CronManager   │    │ ✗ CronManager   │    │ ✗ CronManager   │
│ ✓ EventManager  │    │ ✓ EventManager  │    │ ✓ EventManager  │
│ ✓ DecisionMgr   │    │ ✓ DecisionMgr   │    │ ✓ DecisionMgr   │
│ ✓ ExecutorMgr   │    │ ✓ ExecutorMgr   │    │ ✓ ExecutorMgr   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────────┐
                    │   Database Cluster  │
                    │   (cluster_nodes)   │
                    └─────────────────────┘
```

### 3.2 组件控制策略
- **Collector Manager**: 仅主节点运行，负责指标采集
- **Cron Manager**: 仅主节点运行，负责定时任务触发
- **Event Manager**: 所有节点运行，负责事件处理
- **Decision Manager**: 所有节点运行，负责决策处理
- **Executor Manager**: 所有节点运行，负责执行扩缩容

## 4. 核心流程设计

### 4.1 节点注册流程
1. **获取本机信息**: 获取本机IP地址和主机名
2. **验证实例合法性**: 通过AppTree服务验证IP是否在BNS实例列表中
3. **注册节点**: 将节点信息写入cluster_nodes表，初始角色为slave
4. **启动心跳**: 开始定期心跳更新

```mermaid
sequenceDiagram
    participant Node as 节点
    participant AppTree as AppTree服务
    participant DB as 数据库

    Node->>Node: 获取本机IP
    Node->>AppTree: 查询实例信息
    AppTree-->>Node: 返回实例列表
    Node->>Node: 验证IP是否在实例列表中
    Node->>DB: 注册节点信息(初始角色: slave)
    DB-->>Node: 返回注册结果
```

### 4.2 心跳与选举流程
1. **定期心跳**: 每10秒更新一次last_heartbeat字段
2. **查询活跃节点**: 获取所有在线且心跳未超时的节点
3. **执行选举**: 选择offset最小的节点作为Master
4. **角色更新**: 根据选举结果更新节点角色
5. **组件控制**: 根据角色启动或停止相应组件

```mermaid
sequenceDiagram
    participant Node as 当前节点
    participant DB as 数据库
    participant Other as 其他节点

    loop 每10秒执行一次
        Node->>DB: 更新心跳时间
        Node->>DB: 查询所有活跃节点(按offset排序)
        DB-->>Node: 返回节点列表
        Node->>Node: 过滤心跳超时节点
        Node->>Node: 选择offset最小的节点为Master

        alt 当前节点是Master
            Node->>DB: 更新角色为Master
            Node->>Node: 发送启动信号给Collector和CronManager
        else 当前节点是Slave
            Node->>DB: 更新角色为Slave
            Node->>Node: 发送停止信号给Collector和CronManager
        end
    end
```

### 4.3 故障转移流程
1. **故障检测**: 通过心跳超时检测主节点故障
2. **重新选举**: 从剩余活跃节点中选举新的Master
3. **角色切换**: 新Master启动Collector和CronManager
4. **服务恢复**: 确保扩缩容服务连续性

```mermaid
sequenceDiagram
    participant Master as 主节点
    participant Slave1 as 从节点1
    participant Slave2 as 从节点2
    participant DB as 数据库
    participant Collector as CollectorManager
    participant Cron as CronManager

    Note over Master,Slave2: 正常运行状态

    loop 每10秒心跳检测
        Master->>DB: 更新心跳时间
        Slave1->>DB: 更新心跳时间
        Slave2->>DB: 更新心跳时间

        Master->>DB: 查询活跃节点列表
        Slave1->>DB: 查询活跃节点列表
        Slave2->>DB: 查询活跃节点列表

        Note over Master: Master继续运行
        Note over Slave1,Slave2: Slave保持待机
    end

    Note over Master: ❌ Master节点故障
    Master->>X Master: 停止所有服务

    rect rgb(255, 240, 240)
        Note over Master,Slave2: 故障检测阶段

        Slave1->>DB: 更新心跳时间
        Slave2->>DB: 更新心跳时间

        Slave1->>DB: 查询活跃节点列表
        DB-->>Slave1: 返回节点列表(Master心跳超时)

        Slave2->>DB: 查询活跃节点列表
        DB-->>Slave2: 返回节点列表(Master心跳超时)

        Note over Slave1,Slave2: 检测到Master心跳超时
    end

    rect rgb(240, 255, 240)
        Note over Slave1,Slave2: 选举阶段

        Slave1->>Slave1: 执行选举算法(offset=2)
        Slave2->>Slave2: 执行选举算法(offset=3)

        Note over Slave1: Slave1 offset更小，当选Master
        Note over Slave2: Slave2 保持Slave角色

        Slave1->>DB: 更新角色为Master
        Slave2->>DB: 保持角色为Slave
    end

    rect rgb(240, 240, 255)
        Note over Slave1,Slave2: 角色切换阶段

        Slave1->>Collector: 发送启动信号
        Slave1->>Cron: 发送启动信号

        Collector-->>Slave1: 启动成功
        Cron-->>Slave1: 启动成功

        Note over Slave1: 成为新的Master，开始执行扩缩容任务
        Note over Slave2: 继续保持Slave状态
    end

    Note over Slave1,Slave2: ✅ 故障转移完成

    loop 新的心跳检测周期
        Slave1->>DB: 更新心跳时间(作为Master)
        Slave2->>DB: 更新心跳时间(作为Slave)

        Note over Slave1: 新Master正常运行
        Note over Slave2: Slave继续待机
    end
```

### 4.4 组件状态控制流程

```mermaid
flowchart TD
    Start([节点启动]) --> Register[节点注册]
    Register --> |成功| HeartbeatLoop[启动心跳检测循环]
    Register --> |失败| Exit([退出程序])

    HeartbeatLoop --> UpdateHB[更新心跳时间]
    UpdateHB --> QueryNodes[查询活跃节点列表]
    QueryNodes --> Election[执行选举算法]

    Election --> IsMaster{当前节点是Master?}

    IsMaster --> |是| CheckCurrentRole{当前角色是Master?}
    IsMaster --> |否| CheckCurrentSlave{当前角色是Slave?}

    CheckCurrentRole --> |否| PromoteToMaster[提升为Master]
    CheckCurrentRole --> |是| KeepMaster[保持Master状态]

    CheckCurrentSlave --> |否| DemoteToSlave[降级为Slave]
    CheckCurrentSlave --> |是| KeepSlave[保持Slave状态]

    PromoteToMaster --> UpdateRoleDB1[更新数据库角色为Master]
    UpdateRoleDB1 --> StartComponents[启动Collector和CronManager]
    StartComponents --> LogPromotion[记录提升日志]
    LogPromotion --> Wait[等待下次检测]

    DemoteToSlave --> UpdateRoleDB2[更新数据库角色为Slave]
    UpdateRoleDB2 --> StopComponents[停止Collector和CronManager]
    StopComponents --> LogDemotion[记录降级日志]
    LogDemotion --> Wait

    KeepMaster --> LogMasterStatus[记录Master状态]
    LogMasterStatus --> Wait

    KeepSlave --> LogSlaveStatus[记录Slave状态]
    LogSlaveStatus --> Wait

    Wait --> |10秒后| UpdateHB

    subgraph "组件控制详情"
        StartComponents --> StartCollector[启动CollectorManager]
        StartComponents --> StartCron[启动CronManager]

        StartCollector --> CollectorRunning[开始指标采集]
        StartCron --> CronRunning[开始定时任务]

        StopComponents --> StopCollector[停止CollectorManager]
        StopComponents --> StopCron[停止CronManager]

        StopCollector --> CollectorStopped[停止指标采集]
        StopCron --> CronStopped[停止定时任务]
    end

    subgraph "始终运行的组件"
        AlwaysRunning[EventManager<br/>DecisionManager<br/>ExecutorManager]
    end

    %% 样式定义
    classDef startEnd fill:#e8f5e8,stroke:#4caf50,stroke-width:2px
    classDef process fill:#e3f2fd,stroke:#2196f3,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#ff9800,stroke-width:2px
    classDef masterAction fill:#e8f5e8,stroke:#4caf50,stroke-width:2px
    classDef slaveAction fill:#fce4ec,stroke:#e91e63,stroke-width:2px
    classDef alwaysOn fill:#f3e5f5,stroke:#9c27b0,stroke-width:2px

    class Start,Exit startEnd
    class Register,HeartbeatLoop,UpdateHB,QueryNodes,Election,UpdateRoleDB1,UpdateRoleDB2,LogPromotion,LogDemotion,LogMasterStatus,LogSlaveStatus,Wait process
    class IsMaster,CheckCurrentRole,CheckCurrentSlave decision
    class PromoteToMaster,StartComponents,KeepMaster,StartCollector,StartCron,CollectorRunning,CronRunning masterAction
    class DemoteToSlave,StopComponents,KeepSlave,StopCollector,StopCron,CollectorStopped,CronStopped slaveAction
    class AlwaysRunning alwaysOn
```

## 5. 数据库设计

### 5.1 cluster_nodes 表结构
```sql
CREATE TABLE cluster_nodes (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    offset INT NOT NULL COMMENT '节点偏移量(选举优先级)',
    hostname VARCHAR(128) COMMENT '主机名',
    ip VARCHAR(64) NOT NULL COMMENT 'IP地址',
    role VARCHAR(32) NOT NULL COMMENT '节点角色: master/slave',
    status INT NOT NULL COMMENT '节点状态: 0-离线, 1-在线',
    last_heartbeat TIMESTAMP NOT NULL COMMENT '最后心跳时间',
    dflag INT DEFAULT 0 COMMENT '软删标志',
    last_modify_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 5.2 关键字段说明
- **offset**: 节点选举优先级，值越小优先级越高
- **role**: 当前节点角色，master/slave
- **status**: 节点在线状态，0-离线，1-在线
- **last_heartbeat**: 最后心跳时间，用于故障检测

## 6. 配置参数

### 6.1 关键配置项
```ini
# master.conf
[prod]
bns_info = auto-scaling.op-product
master_check_interval = 10

[dev]
bns_info = SIOD-SRE.yunchuan-kj
master_check_interval = 10
```

### 6.2 参数说明
- **bns_info**: BNS服务名，用于获取集群实例列表
- **master_check_interval**: 主节点检查间隔（秒），默认10秒
- **heartbeat_timeout**: 心跳超时时间 = master_check_interval * 3

## 7. 关键算法

### 7.1 选举算法
```go
func electMasterNode() *dao.ClusterNodes {
    // 心跳超时时间 = 检查间隔 * 3
    heartbeatTimeout := time.Duration(global.MasterCheckInterval*3) * time.Second

    // 获取所有在线节点，按offset升序排列
    activeNodes := getActiveNodesOrderByOffset()

    // 选择第一个心跳未超时的节点作为Master
    now := time.Now()
    for _, node := range activeNodes {
        if now.Sub(node.LastHeartbeat) <= heartbeatTimeout {
            return &node  // 返回offset最小的活跃节点
        }
    }

    return nil  // 没有找到有效的Master节点
}
```

### 7.2 角色更新逻辑
```go
func updateNodeRole(localIP string, masterNode, currentNode *dao.ClusterNodes) bool {
    isMaster := (masterNode.IP == localIP)

    if isMaster {
        // 处理Master角色
        if currentNode.Role != dao.NODE_ROLE_MASTER {
            updateRoleInDB(localIP, dao.NODE_ROLE_MASTER)
            // 启动Collector和CronManager
            sendManagerSignals(true)
        }
        return true
    } else {
        // 处理Slave角色
        if currentNode.Role != dao.NODE_ROLE_SLAVE {
            updateRoleInDB(localIP, dao.NODE_ROLE_SLAVE)
            // 停止Collector和CronManager
            sendManagerSignals(false)
        }
        return false
    }
}
```

### 7.3 组件信号控制
```go
func sendManagerSignals(start bool) {
    if start {
        // 启动关键组件（仅Master执行）
        Engine.CollectorManager.SendStartSignal()
        Engine.CronManager.SendStartSignal()
    } else {
        // 停止关键组件（Slave状态）
        Engine.CollectorManager.SendStopSignal()
        Engine.CronManager.SendStopSignal()
    }
}
```

## 8. 实现细节

### 8.1 节点注册实现
```go
func registerCurrentNode() bool {
    // 1. 获取本机IP地址
    localIP, err := tool_public.GetLocalIP()
    if err != nil {
        return false
    }

    // 2. 验证实例合法性
    instanceList, err := apptree.GetInstanceByBNSWithDisabledV2(global.MasterBns)
    if err != nil {
        return false
    }

    // 3. 检查IP是否在BNS实例列表中
    var foundInstance *libapptreeV2.InstanceInfo
    for _, ins := range instanceList {
        if ins.PrivateIp == localIP {
            foundInstance = &ins
            break
        }
    }

    if foundInstance == nil {
        return false
    }

    // 4. 注册节点信息
    nodeInfo := dao.ClusterNodes{
        Offset:   int(foundInstance.Offset),
        Hostname: hostname,
        IP:       localIP,
        Role:     dao.NODE_ROLE_SLAVE, // 初始注册为slave
    }

    _, r := dao.CreateClusterNodesPtr().RegisterNode(nodeInfo)
    return r.IsOk()
}
```

### 8.2 心跳检测实现
```go
func CheckMaster() {
    ticker := time.NewTicker(time.Duration(global.MasterCheckInterval) * time.Second)
    defer ticker.Stop()
    time.Sleep(3 * time.Second) // 启动延迟

    for range ticker.C {
        // 执行心跳上报和主节点选择
        if isMaster := performHeartbeatAndElection(); isMaster {
            sendManagerSignals(true)
        } else {
            sendManagerSignals(false)
        }
    }
}
```

## 9. 监控与日志

### 9.1 关键日志
- 节点注册成功/失败
- 角色切换（Master ↔ Slave）
- 心跳更新状态
- 选举结果
- 组件启停状态

### 9.2 监控指标
- 当前Master节点信息
- 节点心跳状态
- 角色切换频率
- 故障转移时间
- 组件运行状态

### 9.3 日志示例
```
[INFO] node registered successfully, ID:1, IP:*************, Offset:1001
[INFO] current node promoted to master, IP:*************, Offset:1001
[INFO] sent start signal to collect engine
[INFO] sent start signal to cron task manager
[DEBUG] current node is master, IP:*************, Offset:1001
```

## 10. 故障处理

### 10.1 常见故障场景
1. **主节点宕机**: 自动选举新的主节点
2. **网络分区**: 基于数据库状态进行协调
3. **数据库连接失败**: 节点进入只读模式
4. **心跳超时**: 触发重新选举
5. **BNS服务异常**: 节点注册失败

### 10.2 恢复策略
- **自动恢复**: 节点重启后自动重新注册
- **手动干预**: 通过数据库直接修改节点状态
- **集群重建**: 清空cluster_nodes表重新初始化
- **强制切换**: 手动修改offset优先级

### 10.3 故障排查步骤
1. 检查数据库连接状态
2. 查看节点心跳更新时间
3. 验证BNS实例列表
4. 检查组件运行状态
5. 查看选举日志

## 11. 最佳实践

### 11.1 部署建议
- 至少部署3个节点确保高可用
- 节点分布在不同的物理机或可用区
- 定期检查数据库连接状态
- 配置合适的心跳检测间隔

### 11.2 运维建议
- 监控节点角色切换频率
- 定期清理过期的心跳记录
- 备份cluster_nodes表数据
- 设置告警监控Master节点状态

### 11.3 性能优化
- 合理设置心跳检测间隔
- 优化数据库查询性能
- 减少不必要的角色切换
- 监控组件启停耗时

## 12. API接口设计

### 12.1 节点状态查询接口

| 字段 | 说明 |
|------|------|
| **请求方法** | GET |
| **URL路径** | `/api/v1/cluster/nodes` |
| **请求参数** | 无 |

**响应格式：**
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "nodes": [
            {
                "id": 1,
                "offset": 1001,
                "hostname": "auto-scaler-01",
                "ip": "*************",
                "role": "master",
                "status": 1,
                "last_heartbeat": "2024-01-15 10:30:00",
                "last_modify_time": "2024-01-15 10:30:00"
            }
        ],
        "master_node": {
            "ip": "*************",
            "offset": 1001,
            "hostname": "auto-scaler-01"
        }
    }
}
```

### 12.2 强制主从切换接口

| 字段 | 说明 |
|------|------|
| **请求方法** | POST |
| **URL路径** | `/api/v1/cluster/switch-master` |

**请求参数：**
```json
{
    "target_ip": "*************",
    "force": true
}
```

**响应格式：**
```json
{
    "code": 0,
    "message": "master switch initiated successfully",
    "data": {
        "old_master": "*************",
        "new_master": "*************",
        "switch_time": "2024-01-15 10:35:00"
    }
}
```

### 12.3 节点健康检查接口

| 字段 | 说明 |
|------|------|
| **请求方法** | GET |
| **URL路径** | `/api/v1/cluster/health` |

**响应格式：**
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "cluster_status": "healthy",
        "total_nodes": 3,
        "active_nodes": 3,
        "master_node": "*************",
        "last_election_time": "2024-01-15 09:00:00",
        "heartbeat_interval": 10,
        "heartbeat_timeout": 30
    }
}
```

## 13. 测试策略

### 13.1 单元测试
- 选举算法测试
- 心跳更新测试
- 角色切换测试
- 组件启停测试

### 13.2 集成测试
- 多节点选举测试
- 故障转移测试
- 网络分区测试
- 数据库连接异常测试

### 13.3 压力测试
- 高频心跳测试
- 大量节点选举测试
- 并发角色切换测试

## 14. 版本历史

| 版本 | 日期 | 修改内容 | 作者 |
|------|------|----------|------|
| v1.0 | 2024-01-15 | 初始版本，完成基础主从切换设计 | Auto-Scaler Team |
| v1.1 | 2024-01-15 | 添加API接口设计和测试策略 | Auto-Scaler Team |

## 15. 参考资料

- [Go语言并发编程](https://golang.org/doc/effective_go.html#concurrency)
- [分布式系统选举算法](https://en.wikipedia.org/wiki/Leader_election)
- [数据库高可用设计](https://dev.mysql.com/doc/refman/8.0/en/ha-overview.html)
- [Prometheus监控最佳实践](https://prometheus.io/docs/practices/)
