# Auto-Scaler 主从切换设计文档

## 1. 概述

Auto-Scaler 系统采用主从架构设计，通过心跳检测和选举机制实现高可用性。系统中只有主节点（Master）执行实际的扩缩容任务，从节点（Slave）处于待机状态，当主节点故障时自动进行故障转移。

## 2. 核心设计原则

### 2.1 选举策略
- **基于 Offset 的选举**：系统采用最小 Offset 优先的选举策略
- **心跳检测**：通过定期心跳更新确保节点活跃性
- **故障检测**：心跳超时机制检测节点故障

### 2.2 高可用保证
- **自动故障转移**：主节点故障时自动选举新的主节点
- **服务连续性**：确保扩缩容服务不中断
- **数据一致性**：通过数据库状态同步保证一致性

## 3. 系统架构

### 3.1 节点管理
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Master Node   │    │   Slave Node    │    │   Slave Node    │
│   (Offset: 1)   │    │   (Offset: 2)   │    │   (Offset: 3)   │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ ✓ Collector     │    │ ✗ Collector     │    │ ✗ Collector     │
│ ✓ CronManager   │    │ ✗ CronManager   │    │ ✗ CronManager   │
│ ✓ EventManager  │    │ ✓ EventManager  │    │ ✓ EventManager  │
│ ✓ DecisionMgr   │    │ ✓ DecisionMgr   │    │ ✓ DecisionMgr   │
│ ✓ ExecutorMgr   │    │ ✓ ExecutorMgr   │    │ ✓ ExecutorMgr   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────────┐
                    │   Database Cluster  │
                    │   (cluster_nodes)   │
                    └─────────────────────┘
```

### 3.2 组件控制策略
- **Collector Manager**: 仅主节点运行，负责指标采集
- **Cron Manager**: 仅主节点运行，负责定时任务触发
- **Event Manager**: 所有节点运行，负责事件处理
- **Decision Manager**: 所有节点运行，负责决策处理
- **Executor Manager**: 所有节点运行，负责执行扩缩容

## 4. 核心流程

### 4.1 节点注册流程
```mermaid
sequenceDiagram
    participant Node as 节点
    participant AppTree as AppTree服务
    participant DB as 数据库
    
    Node->>Node: 获取本机IP
    Node->>AppTree: 查询实例信息
    AppTree-->>Node: 返回实例列表
    Node->>Node: 验证IP是否在实例列表中
    Node->>DB: 注册节点信息(初始角色: slave)
    DB-->>Node: 返回注册结果
```

### 4.2 心跳与选举流程
```mermaid
sequenceDiagram
    participant Node as 当前节点
    participant DB as 数据库
    participant Other as 其他节点
    
    loop 每10秒执行一次
        Node->>DB: 更新心跳时间
        Node->>DB: 查询所有活跃节点(按offset排序)
        DB-->>Node: 返回节点列表
        Node->>Node: 过滤心跳超时节点
        Node->>Node: 选择offset最小的节点为Master
        
        alt 当前节点是Master
            Node->>DB: 更新角色为Master
            Node->>Node: 发送启动信号给Collector和CronManager
        else 当前节点是Slave
            Node->>DB: 更新角色为Slave
            Node->>Node: 发送停止信号给Collector和CronManager
        end
    end
```

### 4.3 故障转移流程
```mermaid
sequenceDiagram
    participant Master as 主节点
    participant Slave1 as 从节点1
    participant Slave2 as 从节点2
    participant DB as 数据库
    
    Note over Master: 主节点故障
    Master->>X Master: 停止心跳更新
    
    loop 心跳检测周期
        Slave1->>DB: 更新自己的心跳
        Slave2->>DB: 更新自己的心跳
        Slave1->>DB: 查询活跃节点列表
        Slave2->>DB: 查询活跃节点列表
        
        Note over Slave1,Slave2: 发现Master心跳超时
        
        alt Slave1 offset更小
            Slave1->>DB: 更新角色为Master
            Slave1->>Slave1: 启动Collector和CronManager
            Slave2->>DB: 保持Slave角色
        end
    end
```

## 5. 数据库设计

### 5.1 cluster_nodes 表结构
```sql
CREATE TABLE cluster_nodes (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    offset INT NOT NULL COMMENT '节点偏移量(选举优先级)',
    hostname VARCHAR(128) COMMENT '主机名',
    ip VARCHAR(64) NOT NULL COMMENT 'IP地址',
    role VARCHAR(32) NOT NULL COMMENT '节点角色: master/slave',
    status INT NOT NULL COMMENT '节点状态: 0-离线, 1-在线',
    last_heartbeat TIMESTAMP NOT NULL COMMENT '最后心跳时间',
    dflag INT DEFAULT 0 COMMENT '软删标志',
    last_modify_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 5.2 关键字段说明
- **offset**: 节点选举优先级，值越小优先级越高
- **role**: 当前节点角色，master/slave
- **status**: 节点在线状态，0-离线，1-在线
- **last_heartbeat**: 最后心跳时间，用于故障检测

## 6. 配置参数

### 6.1 关键配置项
```ini
# master.conf
[prod]
bns_info = auto-scaling.op-product
master_check_interval = 10

[dev]
bns_info = SIOD-SRE.yunchuan-kj
master_check_interval = 10
```

### 6.2 参数说明
- **bns_info**: BNS服务名，用于获取集群实例列表
- **master_check_interval**: 主节点检查间隔（秒），默认10秒
- **heartbeat_timeout**: 心跳超时时间 = master_check_interval * 3

## 7. 关键算法

### 7.1 选举算法
```go
func electMasterNode() *dao.ClusterNodes {
    // 心跳超时时间 = 检查间隔 * 3
    heartbeatTimeout := time.Duration(global.MasterCheckInterval*3) * time.Second

    // 获取所有在线节点，按offset升序排列
    activeNodes := getActiveNodesOrderByOffset()

    // 选择第一个心跳未超时的节点作为Master
    now := time.Now()
    for _, node := range activeNodes {
        if now.Sub(node.LastHeartbeat) <= heartbeatTimeout {
            return &node  // 返回offset最小的活跃节点
        }
    }

    return nil  // 没有找到有效的Master节点
}
```

### 7.2 角色更新逻辑
```go
func updateNodeRole(localIP string, masterNode, currentNode *dao.ClusterNodes) bool {
    isMaster := (masterNode.IP == localIP)

    if isMaster {
        // 处理Master角色
        if currentNode.Role != dao.NODE_ROLE_MASTER {
            updateRoleInDB(localIP, dao.NODE_ROLE_MASTER)
            // 启动Collector和CronManager
            sendManagerSignals(true)
        }
        return true
    } else {
        // 处理Slave角色
        if currentNode.Role != dao.NODE_ROLE_SLAVE {
            updateRoleInDB(localIP, dao.NODE_ROLE_SLAVE)
            // 停止Collector和CronManager
            sendManagerSignals(false)
        }
        return false
    }
}
```

### 7.3 组件信号控制
```go
func sendManagerSignals(start bool) {
    if start {
        // 启动关键组件（仅Master执行）
        Engine.CollectorManager.SendStartSignal()
        Engine.CronManager.SendStartSignal()
    } else {
        // 停止关键组件（Slave状态）
        Engine.CollectorManager.SendStopSignal()
        Engine.CronManager.SendStopSignal()
    }
}
```

## 8. 实现细节

### 8.1 节点注册实现
```go
func registerCurrentNode() bool {
    // 1. 获取本机IP地址
    localIP, err := tool_public.GetLocalIP()
    if err != nil {
        return false
    }

    // 2. 验证实例合法性
    instanceList, err := apptree.GetInstanceByBNSWithDisabledV2(global.MasterBns)
    if err != nil {
        return false
    }

    // 3. 检查IP是否在BNS实例列表中
    var foundInstance *libapptreeV2.InstanceInfo
    for _, ins := range instanceList {
        if ins.PrivateIp == localIP {
            foundInstance = &ins
            break
        }
    }

    if foundInstance == nil {
        return false
    }

    // 4. 注册节点信息
    nodeInfo := dao.ClusterNodes{
        Offset:   int(foundInstance.Offset),
        Hostname: hostname,
        IP:       localIP,
        Role:     dao.NODE_ROLE_SLAVE, // 初始注册为slave
    }

    _, r := dao.CreateClusterNodesPtr().RegisterNode(nodeInfo)
    return r.IsOk()
}
```

### 8.2 心跳检测实现
```go
func CheckMaster() {
    ticker := time.NewTicker(time.Duration(global.MasterCheckInterval) * time.Second)
    defer ticker.Stop()
    time.Sleep(3 * time.Second) // 启动延迟

    for range ticker.C {
        // 执行心跳上报和主节点选择
        if isMaster := performHeartbeatAndElection(); isMaster {
            sendManagerSignals(true)
        } else {
            sendManagerSignals(false)
        }
    }
}
```

## 9. 监控与日志

### 9.1 关键日志
- 节点注册成功/失败
- 角色切换（Master ↔ Slave）
- 心跳更新状态
- 选举结果
- 组件启停状态

### 9.2 监控指标
- 当前Master节点信息
- 节点心跳状态
- 角色切换频率
- 故障转移时间
- 组件运行状态

### 9.3 日志示例
```
[INFO] node registered successfully, ID:1, IP:*************, Offset:1001
[INFO] current node promoted to master, IP:*************, Offset:1001
[INFO] sent start signal to collect engine
[INFO] sent start signal to cron task manager
[DEBUG] current node is master, IP:*************, Offset:1001
```

## 10. 故障处理

### 10.1 常见故障场景
1. **主节点宕机**: 自动选举新的主节点
2. **网络分区**: 基于数据库状态进行协调
3. **数据库连接失败**: 节点进入只读模式
4. **心跳超时**: 触发重新选举
5. **BNS服务异常**: 节点注册失败

### 10.2 恢复策略
- **自动恢复**: 节点重启后自动重新注册
- **手动干预**: 通过数据库直接修改节点状态
- **集群重建**: 清空cluster_nodes表重新初始化
- **强制切换**: 手动修改offset优先级

### 10.3 故障排查步骤
1. 检查数据库连接状态
2. 查看节点心跳更新时间
3. 验证BNS实例列表
4. 检查组件运行状态
5. 查看选举日志

## 11. 最佳实践

### 11.1 部署建议
- 至少部署3个节点确保高可用
- 节点分布在不同的物理机或可用区
- 定期检查数据库连接状态
- 配置合适的心跳检测间隔

### 11.2 运维建议
- 监控节点角色切换频率
- 定期清理过期的心跳记录
- 备份cluster_nodes表数据
- 设置告警监控Master节点状态

### 11.3 性能优化
- 合理设置心跳检测间隔
- 优化数据库查询性能
- 减少不必要的角色切换
- 监控组件启停耗时
- **bns_info**: BNS服务名，用于获取集群实例列表
- **master_check_interval**: 主节点检查间隔（秒），默认10秒
- **heartbeat_timeout**: 心跳超时时间 = master_check_interval * 3

## 7. 关键算法

### 7.1 选举算法
```go
func electMasterNode() *dao.ClusterNodes {
    // 心跳超时时间 = 检查间隔 * 3
    heartbeatTimeout := time.Duration(global.MasterCheckInterval*3) * time.Second
    
    // 获取所有在线节点，按offset升序排列
    activeNodes := getActiveNodesOrderByOffset()
    
    // 选择第一个心跳未超时的节点作为Master
    now := time.Now()
    for _, node := range activeNodes {
        if now.Sub(node.LastHeartbeat) <= heartbeatTimeout {
            return &node  // 返回offset最小的活跃节点
        }
    }
    
    return nil  // 没有找到有效的Master节点
}
```

### 7.2 角色更新逻辑
```go
func updateNodeRole(localIP string, masterNode, currentNode *dao.ClusterNodes) bool {
    isMaster := (masterNode.IP == localIP)
    
    if isMaster {
        // 处理Master角色
        if currentNode.Role != dao.NODE_ROLE_MASTER {
            updateRoleInDB(localIP, dao.NODE_ROLE_MASTER)
            // 启动Collector和CronManager
            sendManagerSignals(true)
        }
        return true
    } else {
        // 处理Slave角色
        if currentNode.Role != dao.NODE_ROLE_SLAVE {
            updateRoleInDB(localIP, dao.NODE_ROLE_SLAVE)
            // 停止Collector和CronManager
            sendManagerSignals(false)
        }
        return false
    }
}
```

## 8. 监控与日志

### 8.1 关键日志
- 节点注册成功/失败
- 角色切换（Master ↔ Slave）
- 心跳更新状态
- 选举结果

### 8.2 监控指标
- 当前Master节点信息
- 节点心跳状态
- 角色切换频率
- 故障转移时间

## 9. 故障处理

### 9.1 常见故障场景
1. **主节点宕机**: 自动选举新的主节点
2. **网络分区**: 基于数据库状态进行协调
3. **数据库连接失败**: 节点进入只读模式
4. **心跳超时**: 触发重新选举

### 9.2 恢复策略
- **自动恢复**: 节点重启后自动重新注册
- **手动干预**: 通过数据库直接修改节点状态
- **集群重建**: 清空cluster_nodes表重新初始化

## 10. 最佳实践

### 10.1 部署建议
- 至少部署3个节点确保高可用
- 节点分布在不同的物理机或可用区
- 定期检查数据库连接状态

### 10.2 运维建议
- 监控节点角色切换频率
- 定期清理过期的心跳记录
- 备份cluster_nodes表数据
