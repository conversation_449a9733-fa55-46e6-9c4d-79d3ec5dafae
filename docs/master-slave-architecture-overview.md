# Auto-Scaler 主从切换架构总览

## 架构图说明

本文档包含了 Auto-Scaler 系统主从切换的核心架构图表，用于快速理解系统设计。

## 1. 系统整体架构

### 主从节点分布架构
```
                    Auto-Scaler 集群架构
    ┌─────────────────────────────────────────────────────────────┐
    │                                                             │
    │  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐     │
    │  │ Master Node │    │ Slave Node  │    │ Slave Node  │     │
    │  │ (Offset: 1) │    │ (Offset: 2) │    │ (Offset: 3) │     │
    │  │             │    │             │    │             │     │
    │  │ ✓ Collector │    │ ✗ Collector │    │ ✗ Collector │     │
    │  │ ✓ CronMgr   │    │ ✗ CronMgr   │    │ ✗ CronMgr   │     │
    │  │ ✓ EventMgr  │    │ ✓ EventMgr  │    │ ✓ EventMgr  │     │
    │  │ ✓ DecisionMgr│   │ ✓ DecisionMgr│   │ ✓ DecisionMgr│    │
    │  │ ✓ ExecutorMgr│   │ ✓ ExecutorMgr│   │ ✓ ExecutorMgr│    │
    │  └─────────────┘    └─────────────┘    └─────────────┘     │
    │         │                   │                   │          │
    │         └───────────────────┼───────────────────┘          │
    │                             │                              │
    │              ┌─────────────────────┐                       │
    │              │   Database Cluster  │                       │
    │              │   (cluster_nodes)   │                       │
    │              └─────────────────────┘                       │
    └─────────────────────────────────────────────────────────────┘
                             │
                ┌────────────┼────────────┐
                │            │            │
        ┌───────────┐ ┌─────────────┐ ┌──────────────┐
        │    BNS    │ │ Prometheus  │ │   Scaling    │
        │ AppTree   │ │   Metrics   │ │   Platform   │
        │  Service  │ │   Service   │ │              │
        └───────────┘ └─────────────┘ └──────────────┘
```

## 2. 核心组件说明

### 2.1 主节点 (Master Node)
- **职责**: 执行实际的扩缩容任务
- **运行组件**: 所有组件都处于活跃状态
- **选举条件**: Offset 最小且心跳正常的节点

### 2.2 从节点 (Slave Node)
- **职责**: 待机状态，准备接管主节点工作
- **运行组件**: 仅 EventManager、DecisionManager、ExecutorManager 运行
- **切换条件**: 主节点故障时自动提升为主节点

### 2.3 组件控制策略

| 组件 | Master | Slave | 说明 |
|------|--------|-------|------|
| CollectorManager | ✓ | ✗ | 指标采集，避免重复采集 |
| CronManager | ✓ | ✗ | 定时任务，避免重复触发 |
| EventManager | ✓ | ✓ | 事件处理，所有节点都需要 |
| DecisionManager | ✓ | ✓ | 决策处理，所有节点都需要 |
| ExecutorManager | ✓ | ✓ | 执行器，所有节点都需要 |

## 3. 关键流程

### 3.1 选举流程
1. **心跳检测**: 每10秒更新一次心跳
2. **节点查询**: 获取所有活跃节点列表
3. **选举算法**: 选择 Offset 最小的活跃节点
4. **角色更新**: 更新数据库中的节点角色
5. **组件控制**: 根据角色启停相应组件

### 3.2 故障转移流程
1. **故障检测**: 心跳超时检测主节点故障
2. **重新选举**: 从剩余节点中选举新主节点
3. **角色切换**: 新主节点启动关键组件
4. **服务恢复**: 确保扩缩容服务连续性

## 4. 数据库设计

### cluster_nodes 表核心字段
- **offset**: 选举优先级 (越小优先级越高)
- **ip**: 节点IP地址
- **role**: 节点角色 (master/slave)
- **status**: 在线状态 (0-离线, 1-在线)
- **last_heartbeat**: 最后心跳时间

## 5. 配置参数

### 关键配置
- **master_check_interval**: 心跳检测间隔 (默认10秒)
- **heartbeat_timeout**: 心跳超时时间 (检测间隔 × 3)
- **bns_info**: BNS服务名，用于节点注册验证

## 6. 监控指标

### 关键监控项
- 当前 Master 节点信息
- 节点心跳状态
- 角色切换频率
- 故障转移时间
- 组件运行状态

## 7. 故障场景

### 常见故障及处理
1. **主节点宕机** → 自动选举新主节点
2. **网络分区** → 基于数据库状态协调
3. **数据库异常** → 节点进入只读模式
4. **心跳超时** → 触发重新选举

## 8. 最佳实践

### 部署建议
- 至少3个节点确保高可用
- 节点分布在不同物理机
- 合理设置心跳检测间隔
- 监控角色切换频率

### 运维建议
- 定期检查数据库连接
- 监控节点心跳状态
- 备份集群配置信息
- 设置告警监控

## 9. 技术特点

### 优势
- **高可用性**: 自动故障转移
- **数据一致性**: 基于数据库协调
- **服务连续性**: 无缝切换
- **简单可靠**: 基于 Offset 的简单选举

### 限制
- 依赖数据库可用性
- 心跳检测有延迟
- 需要合理配置超时时间

---

**注意**: 本文档为架构总览，详细实现请参考 `master-slave-switching-design.md`
